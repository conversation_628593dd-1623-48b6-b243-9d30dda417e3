const { bot } = require('../lib/')

// Truth or Dare Game
bot(
  {
    pattern: 'tod ?(truth|dare)?',
    desc: 'Play Truth or Dare with friends',
    type: 'game',
  },
  async (message, match) => {
    const player = message.participant || message.sender
    
    const truths = [
      "What's the most embarrassing thing you've ever done?",
      "Who was your first crush?",
      "What's your biggest fear?",
      "What's the weirdest dream you've ever had?",
      "If you could date anyone in this group, who would it be?",
      "What's your most embarrassing childhood memory?",
      "What's the biggest lie you've ever told?",
      "Who in this group would you trust with your biggest secret?",
      "What's something you've never told your parents?",
      "What's your guilty pleasure?",
      "If you had to delete one app from your phone, what would it be?",
      "What's the most childish thing you still do?",
      "Who was the last person you stalked on social media?",
      "What's your biggest regret?",
      "If you could read minds for a day, whose would you read first?"
    ]
    
    const dares = [
      "Send a funny selfie to the group",
      "Do your best impression of someone in this group",
      "Sing the chorus of your favorite song",
      "Do 10 push-ups and send a video",
      "Text your crush (or ex) 'Hey'",
      "Post an embarrassing photo on your story",
      "Do a silly dance and send a video",
      "Call a random contact and sing happy birthday",
      "Speak in an accent for the next 10 minutes",
      "Let someone else write your status for a day",
      "Do your best animal impression",
      "Send a voice note singing your favorite song",
      "Do a handstand for 30 seconds",
      "Text your mom 'I love you' right now",
      "Make up a rap about someone in this group"
    ]
    
    if (match === 'truth') {
      const randomTruth = truths[Math.floor(Math.random() * truths.length)]
      return await message.send(
        `🤔 *TRUTH for @${player.split('@')[0]}*\n\n` +
        `❓ ${randomTruth}\n\n` +
        `⏰ *You have to answer honestly!*`,
        { mentions: [player] }
      )
    }
    
    if (match === 'dare') {
      const randomDare = dares[Math.floor(Math.random() * dares.length)]
      return await message.send(
        `😈 *DARE for @${player.split('@')[0]}*\n\n` +
        `🎯 ${randomDare}\n\n` +
        `⚡ *You must complete this dare!*`,
        { mentions: [player] }
      )
    }
    
    // Random choice
    const isTruth = Math.random() < 0.5
    const randomQuestion = isTruth ?
      truths[Math.floor(Math.random() * truths.length)] :
      dares[Math.floor(Math.random() * dares.length)]

    return await message.send(
      `🎲 *Truth or Dare for @${player.split('@')[0]}*\n\n` +
      `${isTruth ? '🤔 *TRUTH:*' : '😈 *DARE:*'}\n` +
      `${randomQuestion}\n\n` +
      `💡 *Next time specify:* \`.tod truth\` or \`.tod dare\``,
      { mentions: [player] }
    )
  }
)

// Would You Rather Game
bot(
  {
    pattern: 'wyr',
    desc: 'Would You Rather questions for groups',
    type: 'game',
  },
  async (message) => {
    const questions = [
      "Would you rather have the ability to fly OR be invisible?",
      "Would you rather always be 10 minutes late OR 20 minutes early?",
      "Would you rather have unlimited money OR unlimited time?",
      "Would you rather be famous OR be the best friend of someone famous?",
      "Would you rather live without music OR live without movies?",
      "Would you rather always be hot OR always be cold?",
      "Would you rather have the power to read minds OR predict the future?",
      "Would you rather never use social media again OR never watch another movie?",
      "Would you rather be able to speak all languages OR play all instruments?",
      "Would you rather live in the past OR live in the future?",
      "Would you rather have super strength OR super speed?",
      "Would you rather never eat your favorite food again OR only eat your favorite food?",
      "Would you rather be stuck in traffic for 2 hours OR wait in line for 2 hours?",
      "Would you rather have a rewind button OR a pause button for your life?",
      "Would you rather always tell the truth OR always lie?"
    ]
    
    const randomQuestion = questions[Math.floor(Math.random() * questions.length)]
    
    return await message.send(
      `🤔 *WOULD YOU RATHER*\n\n` +
      `❓ ${randomQuestion}\n\n` +
      `💭 *Everyone answer with your choice and reason!*\n` +
      `🔄 *Type* \`.wyr\` *for another question*`
    )
  }
)

// Never Have I Ever Game
bot(
  {
    pattern: 'nhie',
    desc: 'Never Have I Ever statements',
    type: 'game',
  },
  async (message) => {
    const statements = [
      "Never have I ever skipped school/work to play video games",
      "Never have I ever pretended to be sick to avoid something",
      "Never have I ever stalked someone on social media for hours",
      "Never have I ever forgotten someone's name right after being introduced",
      "Never have I ever laughed so hard I cried",
      "Never have I ever sent a text to the wrong person",
      "Never have I ever fallen asleep during a movie in theaters",
      "Never have I ever googled myself",
      "Never have I ever pretended to understand something I didn't",
      "Never have I ever eaten food that fell on the floor",
      "Never have I ever had a crush on a fictional character",
      "Never have I ever sung in the shower",
      "Never have I ever talked to myself in the mirror",
      "Never have I ever stayed up all night binge-watching a series",
      "Never have I ever accidentally liked an old photo while stalking someone"
    ]
    
    const randomStatement = statements[Math.floor(Math.random() * statements.length)]
    
    return await message.send(
      `🙈 *NEVER HAVE I EVER*\n\n` +
      `💭 ${randomStatement}\n\n` +
      `🙋‍♀️ *If you HAVE done this, say "I have!"*\n` +
      `🙅‍♂️ *If you HAVEN'T, stay quiet*\n\n` +
      `🔄 *Type* \`.nhie\` *for another statement*`
    )
  }
)

// Emoji Puzzle Game
bot(
  {
    pattern: 'emojipuzzle ?(movies|songs|animals|food)?',
    desc: 'Guess the movie/song from emojis',
    type: 'game',
  },
  async (message, match) => {
    const moviePuzzles = [
      { emojis: "🦁👑", answer: "The Lion King", hint: "Disney classic about a young lion" },
      { emojis: "🕷️👨", answer: "Spider-Man", hint: "Marvel superhero with web powers" },
      { emojis: "❄️👸", answer: "Frozen", hint: "Let it go..." },
      { emojis: "🏰👸🐸", answer: "The Princess and the Frog", hint: "Disney princess story" },
      { emojis: "🦈🌊", answer: "Jaws", hint: "Classic shark thriller" },
      { emojis: "👻💀", answer: "Ghostbusters", hint: "Who you gonna call?" },
      { emojis: "🚗⚡", answer: "Cars", hint: "Pixar racing movie" },
      { emojis: "🐠🔍", answer: "Finding Nemo", hint: "Lost fish adventure" }
    ]
    
    const songPuzzles = [
      { emojis: "🌟⭐", answer: "Twinkle Twinkle Little Star", hint: "Children's lullaby" },
      { emojis: "💔😢", answer: "Someone Like You", hint: "Adele hit song" },
      { emojis: "🌈🎵", answer: "Somewhere Over the Rainbow", hint: "Wizard of Oz classic" },
      { emojis: "🕺💃", answer: "Dancing Queen", hint: "ABBA disco hit" },
      { emojis: "🌙🚶‍♂️", answer: "Moonwalk", hint: "Michael Jackson move" },
      { emojis: "🔥❤️", answer: "Burning Love", hint: "Elvis Presley song" }
    ]
    
    const animalPuzzles = [
      { emojis: "🦓", answer: "Zebra", hint: "Black and white striped horse" },
      { emojis: "🐧❄️", answer: "Penguin", hint: "Antarctic bird that can't fly" },
      { emojis: "🦒🌿", answer: "Giraffe", hint: "Tallest animal in the world" },
      { emojis: "🐨🌿", answer: "Koala", hint: "Australian marsupial" },
      { emojis: "🦏🏃‍♂️", answer: "Rhino", hint: "Large horned mammal" }
    ]
    
    const foodPuzzles = [
      { emojis: "🍕🧀", answer: "Pizza", hint: "Italian flatbread with toppings" },
      { emojis: "🍔🍟", answer: "Burger and Fries", hint: "Fast food combo" },
      { emojis: "🍝🍅", answer: "Spaghetti", hint: "Long pasta with sauce" },
      { emojis: "🍣🐟", answer: "Sushi", hint: "Japanese raw fish dish" },
      { emojis: "🌮🌶️", answer: "Tacos", hint: "Mexican folded tortilla" }
    ]
    
    let puzzles, category
    switch (match) {
      case 'movies':
        puzzles = moviePuzzles
        category = 'Movies'
        break
      case 'songs':
        puzzles = songPuzzles
        category = 'Songs'
        break
      case 'animals':
        puzzles = animalPuzzles
        category = 'Animals'
        break
      case 'food':
        puzzles = foodPuzzles
        category = 'Food'
        break
      default:
        puzzles = [...moviePuzzles, ...songPuzzles, ...animalPuzzles, ...foodPuzzles]
        category = 'Mixed'
    }
    
    const randomPuzzle = puzzles[Math.floor(Math.random() * puzzles.length)]
    
    return await message.send(
      `🧩 *EMOJI PUZZLE* - ${category}\n\n` +
      `${randomPuzzle.emojis}\n\n` +
      `🤔 *What am I?*\n\n` +
      `💡 *Hint:* ${randomPuzzle.hint}\n\n` +
      `⏰ *First correct answer wins!*\n` +
      `🔄 *Categories:* movies, songs, animals, food`
    )
  }
)

// Quick Math Race
bot(
  {
    pattern: 'mathrace ?(easy|medium|hard)?',
    desc: 'Quick math problems - race to solve first!',
    type: 'game',
  },
  async (message, match) => {
    const difficulty = match || 'easy'
    let num1, num2, operation, answer, problem
    
    switch (difficulty) {
      case 'easy':
        num1 = Math.floor(Math.random() * 20) + 1
        num2 = Math.floor(Math.random() * 20) + 1
        operation = ['+', '-'][Math.floor(Math.random() * 2)]
        break
      case 'medium':
        num1 = Math.floor(Math.random() * 50) + 10
        num2 = Math.floor(Math.random() * 50) + 10
        operation = ['+', '-', '×'][Math.floor(Math.random() * 3)]
        break
      case 'hard':
        num1 = Math.floor(Math.random() * 100) + 20
        num2 = Math.floor(Math.random() * 100) + 20
        operation = ['+', '-', '×', '÷'][Math.floor(Math.random() * 4)]
        break
    }
    
    switch (operation) {
      case '+':
        answer = num1 + num2
        problem = `${num1} + ${num2}`
        break
      case '-':
        answer = num1 - num2
        problem = `${num1} - ${num2}`
        break
      case '×':
        answer = num1 * num2
        problem = `${num1} × ${num2}`
        break
      case '÷':
        // Ensure clean division
        answer = num1
        num1 = num1 * num2
        problem = `${num1} ÷ ${num2}`
        break
    }
    
    return await message.send(
      `🧮 *MATH RACE* - ${difficulty.toUpperCase()}\n\n` +
      `📊 *Solve this:*\n\n` +
      `**${problem} = ?**\n\n` +
      `⚡ *First correct answer wins!*\n` +
      `🏆 *Just type the number*\n\n` +
      `💡 *Difficulties:* easy, medium, hard`
    )
  }
)

// Random Group Activity Suggestions
bot(
  {
    pattern: 'groupactivity',
    desc: 'Get random group activity suggestions',
    type: 'game',
  },
  async (message) => {
    const activities = [
      "🎵 Share your current favorite song",
      "📸 Everyone send a selfie with a funny face",
      "🍕 Debate: Pineapple on pizza - yes or no?",
      "🎬 Recommend a movie everyone should watch",
      "📱 Share your most used emoji",
      "🎨 Describe your dream vacation in 3 words",
      "🎭 Everyone share their hidden talent",
      "📚 What's the last book you read?",
      "🎮 Favorite childhood game?",
      "🌟 Share a random fun fact about yourself",
      "🎪 If you could have any superpower, what would it be?",
      "🍔 What's your go-to comfort food?",
      "🎵 Sing a line from a song and others guess it",
      "📱 Share your phone's wallpaper",
      "🎨 Draw something and share it (even if it's bad!)"
    ]
    
    const randomActivity = activities[Math.floor(Math.random() * activities.length)]
    
    return await message.send(
      `🎉 *GROUP ACTIVITY TIME!*\n\n` +
      `${randomActivity}\n\n` +
      `👥 *Everyone participate!*\n` +
      `🔄 *Type* \`.groupactivity\` *for another idea*`
    )
  }
)
