const { Client, logger } = require('./lib/client')
const { DATABASE, VERSION } = require('./config')
const { stopInstance } = require('./lib/pm2')
const path = require('path')

// Multi-session support
const sessionId = process.env.SESSION_ID || 'default'
const authPath = process.env.AUTH_PATH || path.join(__dirname, 'auth', sessionId)

const start = async () => {
  logger.info(`levanter ${VERSION}`)
  logger.info(`Session: ${sessionId}`)
  logger.info(`Auth path: ${authPath}`)

  try {
    await DATABASE.authenticate({ retry: { max: 3 } })
  } catch (error) {
    const databaseUrl = process.env.DATABASE_URL
    logger.error({ msg: 'Unable to connect to the database', error: error.message, databaseUrl })
    return stopInstance()
  }
  try {
    const bot = new Client({ sessionId, authPath })
    await bot.connect()
  } catch (error) {
    logger.error(error)
  }
}
start()
