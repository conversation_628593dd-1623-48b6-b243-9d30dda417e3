const { bot, generateList } = require('../lib/')

// Game storage for active games
const activeGames = new Map()
const gameScores = new Map()

// Word Chain Game
bot(
  {
    pattern: 'wordchain ?(start|join|end|score)?',
    desc: 'Play word chain game - last letter starts next word',
    type: 'game',
  },
  async (message, match) => {
    const gameId = message.jid
    const player = message.participant || message.sender
    
    if (match === 'start') {
      activeGames.set(gameId, {
        type: 'wordchain',
        lastWord: '',
        players: new Set([player]),
        words: [],
        turn: player,
        startTime: Date.now()
      })
      
      return await message.send(
        `🔗 *Word Chain Game Started!*\n\n` +
        `🎯 *Rules:*\n` +
        `• Next word must start with last letter of previous word\n` +
        `• No repeating words\n` +
        `• English words only\n\n` +
        `👤 *Started by:* @${player.split('@')[0]}\n\n` +
        `💡 *Commands:*\n` +
        `• Type any word to play\n` +
        `• \`.wordchain join\` - Join game\n` +
        `• \`.wordchain end\` - End game\n\n` +
        `🚀 *Start with any word!*`,
        { quoted: message.data, mentions: [player] }
      )
    }
    
    if (match === 'join') {
      const game = activeGames.get(gameId)
      if (!game || game.type !== 'wordchain') {
        return await message.send('❌ *No word chain game active!*\nStart one with `.wordchain start`')
      }
      
      game.players.add(player)
      return await message.send(
        `✅ *@${player.split('@')[0]} joined the word chain!*\n\n` +
        `👥 *Players:* ${game.players.size}\n` +
        `🔗 *Last word:* ${game.lastWord || 'None yet'}\n` +
        `🎯 *Next letter:* ${game.lastWord ? game.lastWord.slice(-1).toUpperCase() : 'Any'}`,
        { mentions: [player] }
      )
    }
    
    if (match === 'end') {
      const game = activeGames.get(gameId)
      if (!game || game.type !== 'wordchain') {
        return await message.send('❌ *No active word chain game to end!*')
      }
      
      const duration = Math.round((Date.now() - game.startTime) / 1000)
      activeGames.delete(gameId)
      
      return await message.send(
        `🏁 *Word Chain Game Ended!*\n\n` +
        `⏱️ *Duration:* ${duration} seconds\n` +
        `🔗 *Total words:* ${game.words.length}\n` +
        `👥 *Players:* ${game.players.size}\n\n` +
        `📝 *Words used:*\n${game.words.join(' → ')}`
      )
    }
    
    if (match === 'score') {
      const scores = gameScores.get(gameId) || {}
      if (Object.keys(scores).length === 0) {
        return await message.send('📊 *No scores recorded yet!*')
      }
      
      const sortedScores = Object.entries(scores)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
      
      let scoreText = '🏆 *Word Chain Leaderboard:*\n\n'
      sortedScores.forEach(([player, score], index) => {
        const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏅'
        scoreText += `${medal} @${player.split('@')[0]}: ${score} points\n`
      })
      
      return await message.send(scoreText, { 
        mentions: sortedScores.map(([player]) => player) 
      })
    }
    
    // Default - show game status or instructions
    const game = activeGames.get(gameId)
    if (!game || game.type !== 'wordchain') {
      return await message.send(
        `🔗 *Word Chain Game*\n\n` +
        `🎯 *How to play:*\n` +
        `• Start: \`.wordchain start\`\n` +
        `• Join: \`.wordchain join\`\n` +
        `• End: \`.wordchain end\`\n` +
        `• Scores: \`.wordchain score\`\n\n` +
        `📝 *Rules:*\n` +
        `• Next word starts with last letter\n` +
        `• No repeating words\n` +
        `• English words only`
      )
    }
    
    return await message.send(
      `🔗 *Active Word Chain Game*\n\n` +
      `👥 *Players:* ${game.players.size}\n` +
      `🔗 *Last word:* ${game.lastWord || 'None yet'}\n` +
      `🎯 *Next letter:* ${game.lastWord ? game.lastWord.slice(-1).toUpperCase() : 'Any'}\n` +
      `📝 *Words so far:* ${game.words.length}`
    )
  }
)

// Handle word chain gameplay
bot(
  {
    pattern: '.*',
    fromMe: false,
    dontAddCommandList: true,
  },
  async (message, match) => {
    const gameId = message.jid
    const player = message.participant || message.sender
    const game = activeGames.get(gameId)
    
    if (!game || game.type !== 'wordchain') return
    if (!game.players.has(player)) return
    
    const word = match.trim().toLowerCase()
    
    // Check if it's a valid word (basic validation)
    if (!/^[a-z]+$/.test(word) || word.length < 2) return
    
    // Check if word was already used
    if (game.words.includes(word)) {
      return await message.send(
        `❌ *"${word}" was already used!*\n\n` +
        `🔗 *Last word:* ${game.lastWord}\n` +
        `🎯 *Need word starting with:* ${game.lastWord.slice(-1).toUpperCase()}`
      )
    }
    
    // Check if word starts with correct letter
    if (game.lastWord && word[0] !== game.lastWord.slice(-1).toLowerCase()) {
      return await message.send(
        `❌ *"${word}" doesn't start with "${game.lastWord.slice(-1).toUpperCase()}"!*\n\n` +
        `🔗 *Last word:* ${game.lastWord}\n` +
        `🎯 *Need word starting with:* ${game.lastWord.slice(-1).toUpperCase()}`
      )
    }
    
    // Valid word! Add to game
    game.lastWord = word
    game.words.push(word)
    game.turn = player
    
    // Update player score
    const scores = gameScores.get(gameId) || {}
    scores[player] = (scores[player] || 0) + 1
    gameScores.set(gameId, scores)
    
    await message.send(
      `✅ *"${word.toUpperCase()}" accepted!*\n\n` +
      `👤 *Player:* @${player.split('@')[0]}\n` +
      `🎯 *Next word must start with:* ${word.slice(-1).toUpperCase()}\n` +
      `📊 *Your score:* ${scores[player]} points`,
      { mentions: [player] }
    )
  }
)

// Trivia Game
bot(
  {
    pattern: 'trivia ?(start|answer|score|categories)?',
    desc: 'Play trivia quiz game with friends',
    type: 'game',
  },
  async (message, match) => {
    const gameId = message.jid
    const player = message.participant || message.sender
    
    // Trivia questions database
    const triviaQuestions = [
      {
        question: "What is the capital of France?",
        options: ["London", "Berlin", "Paris", "Madrid"],
        correct: 2,
        category: "Geography"
      },
      {
        question: "Which planet is known as the Red Planet?",
        options: ["Venus", "Mars", "Jupiter", "Saturn"],
        correct: 1,
        category: "Science"
      },
      {
        question: "Who painted the Mona Lisa?",
        options: ["Van Gogh", "Picasso", "Da Vinci", "Monet"],
        correct: 2,
        category: "Art"
      },
      {
        question: "What is the largest mammal in the world?",
        options: ["Elephant", "Blue Whale", "Giraffe", "Hippo"],
        correct: 1,
        category: "Nature"
      },
      {
        question: "In which year did World War II end?",
        options: ["1944", "1945", "1946", "1947"],
        correct: 1,
        category: "History"
      },
      {
        question: "What is the chemical symbol for gold?",
        options: ["Go", "Gd", "Au", "Ag"],
        correct: 2,
        category: "Science"
      },
      {
        question: "Which country invented pizza?",
        options: ["France", "Italy", "Greece", "Spain"],
        correct: 1,
        category: "Food"
      },
      {
        question: "What is the fastest land animal?",
        options: ["Lion", "Cheetah", "Leopard", "Tiger"],
        correct: 1,
        category: "Nature"
      }
    ]
    
    if (match === 'start') {
      const randomQuestion = triviaQuestions[Math.floor(Math.random() * triviaQuestions.length)]
      
      activeGames.set(gameId, {
        type: 'trivia',
        question: randomQuestion,
        players: new Set(),
        answers: new Map(),
        startTime: Date.now(),
        timeLimit: 30000 // 30 seconds
      })
      
      const optionsText = randomQuestion.options
        .map((option, index) => `${index + 1}. ${option}`)
        .join('\n')
      
      setTimeout(async () => {
        const game = activeGames.get(gameId)
        if (game && game.type === 'trivia') {
          await message.send(
            `⏰ *Time's up!*\n\n` +
            `✅ *Correct answer:* ${randomQuestion.options[randomQuestion.correct]}\n` +
            `📚 *Category:* ${randomQuestion.category}\n\n` +
            `🎯 *Start another round with* \`.trivia start\``
          )
          activeGames.delete(gameId)
        }
      }, 30000)
      
      return await message.send(
        `🧠 *TRIVIA TIME!*\n\n` +
        `📚 *Category:* ${randomQuestion.category}\n` +
        `❓ *Question:*\n${randomQuestion.question}\n\n` +
        `📝 *Options:*\n${optionsText}\n\n` +
        `⏰ *You have 30 seconds!*\n` +
        `💡 *Reply with the number (1-4)*`
      )
    }
    
    // Handle answer
    if (/^[1-4]$/.test(message.text)) {
      const game = activeGames.get(gameId)
      if (!game || game.type !== 'trivia') return
      
      const answer = parseInt(message.text) - 1
      const isCorrect = answer === game.question.correct
      
      if (game.answers.has(player)) {
        return await message.send('❌ *You already answered this question!*')
      }
      
      game.answers.set(player, { answer, isCorrect, time: Date.now() - game.startTime })
      game.players.add(player)
      
      // Update scores
      if (isCorrect) {
        const scores = gameScores.get(gameId) || {}
        const timeBonus = Math.max(1, Math.floor((30000 - (Date.now() - game.startTime)) / 1000))
        scores[player] = (scores[player] || 0) + (10 + timeBonus)
        gameScores.set(gameId, scores)
        
        await message.send(
          `🎉 *CORRECT!* @${player.split('@')[0]}\n\n` +
          `⚡ *Speed bonus:* +${timeBonus} points\n` +
          `📊 *Total score:* ${scores[player]} points`,
          { mentions: [player] }
        )
        
        // End game if someone got it right
        activeGames.delete(gameId)
      } else {
        await message.send(
          `❌ *Wrong answer* @${player.split('@')[0]}\n\n` +
          `🤔 *Keep trying! Time remaining...*`,
          { mentions: [player] }
        )
      }
    }
    
    if (match === 'score') {
      const scores = gameScores.get(gameId) || {}
      if (Object.keys(scores).length === 0) {
        return await message.send('📊 *No trivia scores yet!*')
      }
      
      const sortedScores = Object.entries(scores)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
      
      let scoreText = '🧠 *Trivia Leaderboard:*\n\n'
      sortedScores.forEach(([player, score], index) => {
        const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏅'
        scoreText += `${medal} @${player.split('@')[0]}: ${score} points\n`
      })
      
      return await message.send(scoreText, { 
        mentions: sortedScores.map(([player]) => player) 
      })
    }
    
    return await message.send(
      `🧠 *Trivia Game*\n\n` +
      `🎯 *Commands:*\n` +
      `• \`.trivia start\` - Start new question\n` +
      `• \`.trivia score\` - View leaderboard\n\n` +
      `📚 *Categories:* Geography, Science, Art, Nature, History, Food\n\n` +
      `🏆 *How to play:*\n` +
      `• Answer questions by typing 1-4\n` +
      `• Faster answers get bonus points\n` +
      `• Compete with friends!`
    )
  }
)
